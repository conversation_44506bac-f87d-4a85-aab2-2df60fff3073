import React, { useState, useEffect, Suspense, lazy, useRef } from "react";
import Footer from "../components/Footer";
import LandingBackground from "../components/LandingBackground";
import { useLanguageNavigate } from "../hooks/useLanguageNavigate";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import SEO from "../components/SEO";
import { mobileScreen } from "../lib/utils";
import { useTheme } from "../contexts/ThemeContext";
import TiltImage from "../blocks/Components/TiltImage";
import HoroscopePromoBlock from "../components/horoscope/HoroscopePromoBlock";
// import { blogPostsByLang } from "../data/blogPosts";
import { CdnLazyImage, getImageUrl } from '../components/CdnImageExport';
// import { getSEOConfig } from "../lib/SEOConfig";

// 延迟加载非关键组件
const SpotlightCard = lazy(
  () => import("../blocks/Components/SpotlightCard/SpotlightCard")
);
// const VideoSection = lazy(() => import("../components/VideoSection"));

// 添加全局样式
const globalStyles = `
  @keyframes scroll {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-50%);
    }
  }
  
  .reader-carousel {
    animation: scroll 30s linear infinite;
  }
`;

// 读者轮播组件 - 移到主组件外部
const ReaderCarousel = React.memo(() => {
  // 读者图片数组 - 使用常量避免重新创建
  const readerImages = [
    {
      name: "Claire",
      src: "images-optimized/readers/Claire.webp",
      mobileSrc: "images-optimized/readers/Claire.webp",
    },
    {
      name: "Molly",
      src: "images-optimized/readers/Molly.webp",
      mobileSrc: "images-optimized/readers/Molly.webp",
    },
    {
      name: "Elias",
      src: "images-optimized/readers/Elias.webp",
      mobileSrc: "images-optimized/readers/Elias.webp",
    },
    {
      name: "Raven",
      src: "images-optimized/readers/Raven.webp",
      mobileSrc: "images-optimized/readers/Raven.webp",
    },
    {
      name: "Vincent",
      src: "images-optimized/readers/Vincent.webp",
      mobileSrc: "images-optimized/readers/Vincent.webp",
    },
    {
      name: "Aurora",
      src: "images-optimized/readers/Aurora.webp",
      mobileSrc: "images-optimized/readers/Aurora.webp",
    }
  ];

  // 使用useTranslation hook
  const { t } = useTranslation();

  return (
    <div className="mt-12 sm:mt-16 w-full max-w-4xl mx-auto mb-16">
      <div className="relative overflow-hidden">
        <div
          className="flex reader-carousel"
          style={{
            width: "fit-content",
            willChange: "transform",
          }}
        >
          {/* 第一组图片 */}
          {readerImages.map((image) => (
            <div
              key={`first-${image.name}`}
              className="w-[300px] flex-shrink-0 p-2 sm:p-4"
              style={{
                minWidth: "300px",
              }}
            >
              <div
                className="rounded-lg overflow-hidden shadow-lg"
                style={{
                  boxShadow: "0 0 20px rgba(168, 85, 247, 0.3)",
                  height: "100%",
                  pointerEvents: "none",
                }}
              >
                <CdnLazyImage
                  src={mobileScreen() ? image.mobileSrc : image.src}
                  alt={t("home.reader_image_alt", { name: image.name })}
                  className="w-full h-full object-cover select-none"
                  style={{ aspectRatio: "3/4" }}
                  draggable="false"
                />
              </div>
            </div>
          ))}
          
          {/* 第二组图片重复显示 */}
          {readerImages.map((image) => (
            <div
              key={`second-${image.name}`}
              className="w-[300px] flex-shrink-0 p-2 sm:p-4"
              style={{
                minWidth: "300px",
              }}
            >
              <div
                className="rounded-lg overflow-hidden shadow-lg"
                style={{
                  boxShadow: "0 0 20px rgba(168, 85, 247, 0.3)",
                  height: "100%",
                  pointerEvents: "none",
                }}
              >
                <CdnLazyImage
                  src={mobileScreen() ? image.mobileSrc : image.src}
                  alt={t("home.reader_image_alt", { name: image.name })}
                  className="w-full h-full object-cover select-none"
                  style={{ aspectRatio: "3/4" }}
                  draggable="false"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
});

// 使用CdnLazyImage组件替代原来的LazyImage组件

const NewPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const cardBackImage = getImageUrl("images-optimized/home-images-001-sm.webp");
  const { navigate } = useLanguageNavigate();

  const [selectedDrawMethod, setSelectedDrawMethod] = useState('traditional');
  const optionRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [cursorPosition, setCursorPosition] = useState({ top: 0, height: 0 });

  // 更新光标位置的函数
  const updateCursorPosition = () => {
    const index = selectedDrawMethod === 'traditional' ? 0 : 
                 selectedDrawMethod === 'intuitive' ? 1 : 2;
    
    const currentOption = optionRefs.current[index];
    if (currentOption) {
      const { offsetTop, clientHeight } = currentOption;
      setCursorPosition({
        top: offsetTop + 20, // 稍微向下偏移，使其更居中
        height: clientHeight - 40 // 稍微缩短高度
      });
    }
  };

  // 当选择改变或组件挂载时更新光标位置
  useEffect(() => {
    updateCursorPosition();
    // 添加窗口大小变化的监听，确保响应式布局下光标位置正确
    window.addEventListener('resize', updateCursorPosition);
    return () => window.removeEventListener('resize', updateCursorPosition);
  }, [selectedDrawMethod]);
  
  // 添加自动切换功能 - 仅在桌面端
  useEffect(() => {
    // 检查是否为移动端
    const isMobile = window.innerWidth < 768; // md断点通常是768px
    
    // 如果是移动端，不启用自动切换
    if (isMobile) return;
    
    const autoChangeInterval = setInterval(() => {
      // 循环切换三个选项
      setSelectedDrawMethod(prevMethod => {
        if (prevMethod === 'traditional') return 'intuitive';
        if (prevMethod === 'intuitive') return 'number';
        return 'traditional';
      });
    }, 5000); // 每5秒切换一次
    
    return () => clearInterval(autoChangeInterval); // 清理定时器
  }, []);

  const handleStartReading = () => {
    navigate('/home');
  };

  const handleCardBackSettings = () => {
    navigate("/home");
  };

  return (
    <div className="min-h-screen flex flex-col relative">
      <SEO />

      <style dangerouslySetInnerHTML={{ __html: globalStyles }} />
      <LandingBackground />

      <main className="flex-grow container mx-auto px-3 sm:px-6 pt-0">
        <div className="relative z-10 max-w-7xl mx-auto">
          <div
            className="text-center mb-4 sm:mb-6 mt-6 sm:mt-8 md:mt-10 px-2 sm:px-4 relative"
            style={{ zIndex: 5 }}
          >
            <div className="space-y-4 sm:space-y-6">
              <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold">
                {i18n.language === 'ja' ? (
                  <>
                    <div className="text-white">無料タロット占い</div>
                    <div className="mt-2">
                      <span style={{color: "#C66CCD"}} className="text-purple-400">イエスノー占い</span>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="text-white">{t("home.hero.free_online")}</div>
                    <div className="mt-2">
                      <span style={{color: "#C66CCD"}} className="text-purple-400">{t("home.hero.ai_tarot")}</span>
                      <span className="text-white">{i18n.language === 'en' ? " " : ""}{t("home.hero.divination")}</span>
                    </div>
                  </>
                )}
              </h1>
              <div className="pt-1 sm:pt-2">
                <h2 className="text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto mb-0">
                  {/* 桌面版本 - 中文显示一行四个，英语显示特定标签，其他语言显示两行两个 */}
                  <div className="hidden sm:flex sm:flex-col sm:items-center sm:justify-center px-2">
                    {i18n.language === 'zh-CN' || i18n.language === 'zh-TW' ? (
                      <div className="flex items-center justify-center">
                        <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t("home.hero.online_tarot_draw")}</span>
                        <span className="mx-2 text-purple-500">✦</span>
                        <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t("home.hero.intuitive_tarot_reading")}</span>
                        <span className="mx-2 text-purple-500">✦</span>
                        <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t("home.hero.daily_fortune_divination")}</span>
                        <span className="mx-2 text-purple-500">✦</span>
                        <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t("home.hero.accurate_free_fortune_telling")}</span>
                      </div>
                    ) : i18n.language === 'en' ? (
                      <div className="flex items-center justify-center">
                        <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>One Card Tarot</span>
                        <span className="mx-2 text-purple-500">✦</span>
                        <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>Yes or No</span>
                        <span className="mx-2 text-purple-500">✦</span>
                        <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>Love & Career Readings</span>
                      </div>
                    ) : (
                      <>
                        <div className="flex items-center justify-center mb-2">
                          <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t("home.hero.online_tarot_draw")}</span>
                          <span className="mx-2 text-purple-500">✦</span>
                          <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t("home.hero.intuitive_tarot_reading")}</span>
                        </div>
                        <div className="flex items-center justify-center">
                          <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t("home.hero.daily_fortune_divination")}</span>
                          <span className="mx-2 text-purple-500">✦</span>
                          <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t("home.hero.accurate_free_fortune_telling")}</span>
                        </div>
                      </>
                    )}
                  </div>
                  
                  {/* 移动版本 - 英文显示特定标签，其他语言显示两行两个 */}
                  <div className="flex flex-col sm:hidden text-center px-2 space-y-2">
                    {i18n.language === 'en' ? (
                      <>
                        <div className="flex items-center justify-center">
                          <span className="text-sm md:text-lg font-medium italic" style={{color: "#CFADF4"}}>One Card Tarot</span>
                          <span className="mx-1 md:mx-2 text-purple-500">✦</span>
                          <span className="text-sm md:text-lg font-medium italic" style={{color: "#CFADF4"}}>Yes or No</span>
                        </div>
                        <div className="flex items-center justify-center">
                          <span className="text-sm md:text-lg font-medium italic" style={{color: "#CFADF4"}}>Love & Career Readings</span>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="flex items-center justify-center">
                          <span className="text-sm md:text-lg font-medium italic" style={{color: "#CFADF4"}}>{t("home.hero.online_tarot_draw")}</span>
                          <span className="mx-1 md:mx-2 text-purple-500">✦</span>
                          <span className="text-sm md:text-lg font-medium italic" style={{color: "#CFADF4"}}>{t("home.hero.intuitive_tarot_reading")}</span>
                        </div>
                        <div className="flex items-center justify-center">
                          <span className="text-sm md:text-lg font-medium italic" style={{color: "#CFADF4"}}>{t("home.hero.daily_fortune_divination")}</span>
                          <span className="mx-1 md:mx-2 text-purple-500">✦</span>
                          <span className="text-sm md:text-lg font-medium italic" style={{color: "#CFADF4"}}>{t("home.hero.accurate_free_fortune_telling")}</span>
                        </div>
                      </>
                    )}
                  </div>
                </h2>
              </div>
            </div>
          </div>

          <div
            className="flex flex-col items-center mt-4 sm:mt-8 space-y-8 sm:space-y-16 relative"
            style={{ zIndex: 5 }}
          >
            <motion.div className="relative">
              <div className="w-[160px] h-[285px] sm:w-[240px] sm:h-[428px]">
                <TiltImage
                  imageUrl={cardBackImage}
                  alt={t("home.card_back_preview_alt")}
                  className="cursor-pointer"
                  onClick={handleCardBackSettings}
                />
              </div>
            </motion.div>

            <button
              onClick={handleStartReading}
              className="w-[280px] sm:w-[320px] relative group"
            >
              <div
                className="absolute -inset-0.5 bg-gradient-to-r from-fuchsia-500 to-purple-600 
                            rounded-xl blur opacity-60 group-hover:opacity-100 transition duration-200"
              ></div>
              <div className={`w-full relative h-[52px] sm:h-[64px] ${theme === 'light' ? 'bg-white' : 'bg-black'} rounded-xl leading-none flex items-center justify-center`}>
                <span className={`${theme === 'light' ? 'text-black' : 'text-white'} text-[20px] sm:text-2xl font-normal whitespace-nowrap font-['Inter']`}>
                  {t("home.hero.start_free_draw_button")}
                </span>
              </div>
            </button>
          </div>

          {/* 新增占卜类型板块 */}
          <div className="max-w-6xl mx-auto mt-24 sm:mt-32 px-4 sm:px-6">
            <div className="text-center mb-10">
              <h2 className={`text-2xl sm:text-3xl font-bold ${
                theme === 'light' ? 'text-purple-800' : 'text-white'
              }`}>
                {t("home.tarot_types.section_title")}
              </h2>
              <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
              <p className={`${
                theme === 'light' ? 'text-gray-700' : 'text-gray-300'
              } text-lg max-w-3xl mx-auto`}>
                {t("home.tarot_types.section_subtitle")}
              </p>
            </div>

            <div className="space-y-16 sm:space-y-24">
              {/* AI塔羅牌占卜 */}
              <div className="flex flex-col md:flex-row items-center gap-8 md:gap-12">
                                  <div className="md:w-1/2 order-2 md:order-1 relative hidden md:block">
                    <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '16/9' }}>
                      <CdnLazyImage 
                        src={`/images-optimized/landing/AI-Tarot-Divination-${i18n.language}.webp`}
                        alt={t("home.tarot_types.ai_tarot.title")}
                        className="w-full h-full object-cover"
                      />
                    </div>
                </div>
                <div className="md:w-1/2 order-1 md:order-2 mt-0 md:mt-0">
                  <h3 className={`text-2xl sm:text-3xl font-bold mb-4 ${
                    theme === 'light' ? 'text-gray-800' : 'text-white'
                  }`}>{t("home.tarot_types.ai_tarot.title")}</h3>
                  <p className={`${
                    theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                  } mb-6 text-base sm:text-lg`}>
                    {t("home.tarot_types.ai_tarot.description")}
                  </p>
                  
                  {/* 移动端图片显示在文案下方、按钮上方 */}
                  <div className="block md:hidden mb-6 mt-4">
                    <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '16/9' }}>
                      <CdnLazyImage 
                        src={`/images-optimized/landing/AI-Tarot-Divination-${i18n.language}.webp`}
                        alt={t("home.tarot_types.ai_tarot.title")}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                  
                  <button
                    onClick={handleStartReading}
                    className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full hover:shadow-lg transition duration-300"
                  >
                    {t("home.tarot_types.ai_tarot.button")}
                  </button>
                </div>
              </div>

              {/* 是否塔罗 */}
              <div className="flex flex-col md:flex-row-reverse items-center gap-8 md:gap-12">
                <div className="md:w-1/2 order-2 md:order-1 relative hidden md:block">
                  <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '16/9' }}>
                    <CdnLazyImage 
                        src="/images-optimized/landing/Yes-or-No-Tarot.webp" 
                        alt={t("home.tarot_types.yes_no_tarot.title")}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
                <div className="md:w-1/2 order-1 md:order-2 mt-0 md:mt-0">
                  <h3 className={`text-2xl sm:text-3xl font-bold mb-4 ${
                    theme === 'light' ? 'text-gray-800' : 'text-white'
                  }`}>{t("home.tarot_types.yes_no_tarot.title")}</h3>
                  <p className={`${
                    theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                  } mb-6 text-base sm:text-lg`}>
                    {t("home.tarot_types.yes_no_tarot.description")}
                  </p>
                  
                  {/* 移动端图片显示在文案下方、按钮上方 */}
                  <div className="block md:hidden mb-6 mt-4">
                    <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '16/9' }}>
                      <CdnLazyImage 
                        src="/images-optimized/landing/Yes-or-No-Tarot.webp" 
                        alt={t("home.tarot_types.yes_no_tarot.title")}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                  
                  <button
                    onClick={() => navigate('/yes-no-tarot')}
                    className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full hover:shadow-lg transition duration-300"
                  >
                    {t("home.tarot_types.yes_no_tarot.button")}
                  </button>
                </div>
              </div>

              {/* 每日运势 */}
              <div className="flex flex-col md:flex-row items-center gap-8 md:gap-12">
                <div className="md:w-1/2 order-2 md:order-1 relative hidden md:block">
                  <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '16/9' }}>
                    <CdnLazyImage 
                        src={`/images-optimized/landing/Daily-fortune-telling-${i18n.language}.webp`} 
                        alt={t("home.tarot_types.daily_fortune.title")}
                      className="w-full h-full object-cover" 
                    />
                  </div>
                </div>
                <div className="md:w-1/2 order-1 md:order-2 mt-0 md:mt-0">
                  <h3 className={`text-2xl sm:text-3xl font-bold mb-4 ${
                    theme === 'light' ? 'text-gray-800' : 'text-white'
                  }`}>{t("home.tarot_types.daily_fortune.title")}</h3>
                  <p className={`${
                    theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                  } mb-6 text-base sm:text-lg`}>
                    {t("home.tarot_types.daily_fortune.description")}
                  </p>
                  
                  {/* 移动端图片显示在文案下方、按钮上方 */}
                  <div className="block md:hidden mb-6 mt-4">
                    <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '16/9' }}>
                      <CdnLazyImage 
                        src={`/images-optimized/landing/Daily-fortune-telling-${i18n.language}.webp`} 
                        alt={t("home.tarot_types.daily_fortune.title")}
                        className="w-full h-full object-cover" 
                      />
                    </div>
                  </div>
                  
                  <button
                    onClick={() => navigate('/daily-fortune')}
                    className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full hover:shadow-lg transition duration-300"
                  >
                    {t("home.tarot_types.daily_fortune.button")}
                  </button>
                </div>
              </div>

              {/* 爱情星座运势 */}
              <div className="flex flex-col md:flex-row-reverse items-center gap-8 md:gap-12">
                <div className="md:w-1/2 order-2 md:order-1 relative hidden md:block">
                  <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '16/9' }}>
                    <CdnLazyImage 
                      src="/images-optimized/landing/Love-Tarot-Divination.webp" 
                      alt={t("horoscope.love.title")}
                      className="w-full h-full object-cover" 
                    />
                  </div>
                </div>
                <div className="md:w-1/2 order-1 md:order-2 mt-0 md:mt-0">
                  <h3 className={`text-2xl sm:text-3xl font-bold mb-4 ${
                    theme === 'light' ? 'text-gray-800' : 'text-white'
                  }`}>{t("horoscope.love.title")}</h3>
                  <p className={`${
                    theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                  } mb-6 text-base sm:text-lg`}>
                    {t("horoscope.love.description")}
                  </p>
                  
                  {/* 移动端图片显示在文案下方、按钮上方 */}
                  <div className="block md:hidden mb-6 mt-4">
                    <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '16/9' }}>
                      <CdnLazyImage 
                        src="/images-optimized/landing/Love-Tarot-Divination.webp" 
                        alt={t("horoscope.love.title")}
                        className="w-full h-full object-cover" 
                      />
                    </div>
                  </div>
                  
                  <button
                    onClick={() => navigate('/horoscope/love-horoscope')}
                    className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full hover:shadow-lg transition duration-300"
                  >
                    {t("horoscope.love.button")}
                  </button>
                </div>
              </div>
            </div>
          </div>



          {/* 新增使用方式板块 */}
          <div className="max-w-6xl mx-auto mt-28 sm:mt-36 px-4 sm:px-6">
            <div className="text-center mb-10">
              <h2 className={`text-2xl sm:text-3xl font-bold ${
                theme === 'light' ? 'text-purple-800' : 'text-white'
              }`}>
                {t("home.draw_methods.section_title")}
              </h2>
              <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
              <p className={`${
                theme === 'light' ? 'text-gray-700' : 'text-gray-300'
              } text-lg max-w-3xl mx-auto`}>
                {t("home.draw_methods.section_subtitle")}
              </p>
            </div>

            {/* 桌面端：左侧文案右侧图片的布局 */}
            <div className="hidden md:flex flex-row gap-12 items-center">
              {/* 左侧文案区域 */}
              <div className="w-1/2">
                <div className="space-y-6 relative">
                  {/* 添加移动的光标指示器 */}
                  <div 
                    className="absolute left-0 w-1 bg-purple-500 transition-all duration-300 ease-in-out"
                    style={{
                      top: `${cursorPosition.top}px`,
                      height: `${cursorPosition.height}px`,
                    }}
                  ></div>
                  
                  {/* 传统线上抽签 */}
                  <div 
                    ref={el => optionRefs.current[0] = el}
                    className={`p-6 cursor-pointer transition-all duration-300 pl-8 ${
                      selectedDrawMethod === 'traditional' 
                        ? `${theme === 'light' ? 'text-purple-700' : 'text-purple-300'}`
                        : `${theme === 'light' ? 'hover:text-purple-700' : 'hover:text-purple-300'}`
                    }`}
                    onClick={() => {
                      setSelectedDrawMethod('traditional');
                    }}
                  >
                    <div>
                      <h3 className={`text-xl font-semibold mb-3 ${
                        theme === 'light' ? 'text-gray-800' : 'text-white'
                      }`}>{t("home.draw_methods.traditional.title")}</h3>
                      <p className={`${
                        theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                      }`}>
                        {t("home.draw_methods.traditional.description")}
                      </p>
                    </div>
                  </div>

                  {/* 幸运数字选牌 */}
                  <div 
                    ref={el => optionRefs.current[1] = el}
                    className={`p-6 cursor-pointer transition-all duration-300 pl-8 ${
                      selectedDrawMethod === 'intuitive' 
                        ? `${theme === 'light' ? 'text-purple-700' : 'text-purple-300'}`
                        : `${theme === 'light' ? 'hover:text-purple-700' : 'hover:text-purple-300'}`
                    }`}
                    onClick={() => {
                      setSelectedDrawMethod('intuitive');
                    }}
                  >
                    <div>
                      <h3 className={`text-xl font-semibold mb-3 ${
                        theme === 'light' ? 'text-gray-800' : 'text-white'
                      }`}>{t("home.draw_methods.lucky_number.title")}</h3>
                      <p className={`${
                        theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                      }`}>
                        {t("home.draw_methods.lucky_number.description")}
                      </p>
                    </div>
                  </div>

                  {/* 塔罗牌解读 */}
                  <div 
                    ref={el => optionRefs.current[2] = el}
                    className={`p-6 cursor-pointer transition-all duration-300 pl-8 ${
                      selectedDrawMethod === 'number' 
                        ? `${theme === 'light' ? 'text-purple-700' : 'text-purple-300'}`
                        : `${theme === 'light' ? 'hover:text-purple-700' : 'hover:text-purple-300'}`
                    }`}
                    onClick={() => {
                      setSelectedDrawMethod('number');
                    }}
                  >
                    <div>
                      <h3 className={`text-xl font-semibold mb-3 ${
                        theme === 'light' ? 'text-gray-800' : 'text-white'
                      }`}>{t("home.draw_methods.custom.title")}</h3>
                      <p className={`${
                        theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                      }`}>
                        {t("home.draw_methods.custom.description")}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* 右侧图片区域 */}
              <div className="w-1/2 flex items-center justify-center">
                <div className="bg-gradient-to-br from-purple-100 to-pink-200 dark:from-purple-900 dark:to-pink-900 p-1 rounded-2xl shadow-lg aspect-square w-full">
                  <div className="rounded-xl overflow-hidden relative h-full">
                    {/* 传统线上抽签图片 */}
                    <div className={`transition-opacity duration-500 ${selectedDrawMethod === 'traditional' ? 'opacity-100' : 'opacity-0 absolute inset-0'}`}>
                      <CdnLazyImage 
                        src={`/images-optimized/landing/Traditional-online-card-draw-${i18n.language}.webp`} 
                        alt={t("home.draw_methods.traditional.title")}
                        className="w-full h-full object-cover" 
                      />
                    </div>
                    
                    {/* 幸运数字选牌图片 */}
                    <div className={`transition-opacity duration-500 ${selectedDrawMethod === 'intuitive' ? 'opacity-100' : 'opacity-0 absolute inset-0'}`}>
                      <CdnLazyImage 
                        src={`/images-optimized/landing/Lucky-Number-Picker-${i18n.language}.webp`} 
                        alt={t("home.draw_methods.lucky_number.title")}
                        className="w-full h-full object-cover" 
                      />
                    </div>
                    
                    {/* 塔罗牌解读图片 */}
                    <div className={`transition-opacity duration-500 ${selectedDrawMethod === 'number' ? 'opacity-100' : 'opacity-0 absolute inset-0'}`}>
                      <CdnLazyImage 
                        src={`/images-optimized/landing/Self-selected-Tarot-reading-${i18n.language}.webp`} 
                        alt={t("home.draw_methods.custom.title")}
                        className="w-full h-full object-cover" 
                      />
                    </div>
                    
                    {/* 添加进度指示器 */}
                    <div className="absolute bottom-3 left-0 right-0 flex justify-center space-x-2">
                      <div 
                        className={`h-1.5 w-6 rounded-full transition-all duration-300 ${selectedDrawMethod === 'traditional' ? 'bg-white' : 'bg-white/30'}`}
                        onClick={() => {
                          setSelectedDrawMethod('traditional');
                        }}
                        style={{ cursor: 'pointer' }}
                      ></div>
                      <div 
                        className={`h-1.5 w-6 rounded-full transition-all duration-300 ${selectedDrawMethod === 'intuitive' ? 'bg-white' : 'bg-white/30'}`}
                        onClick={() => {
                          setSelectedDrawMethod('intuitive');
                        }}
                        style={{ cursor: 'pointer' }}
                      ></div>
                      <div 
                        className={`h-1.5 w-6 rounded-full transition-all duration-300 ${selectedDrawMethod === 'number' ? 'bg-white' : 'bg-white/30'}`}
                        onClick={() => {
                          setSelectedDrawMethod('number');
                        }}
                        style={{ cursor: 'pointer' }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 移动端：上文下图排列 */}
            <div className="md:hidden space-y-16">
              {/* 传统线上抽牌 */}
              <div className="space-y-4">
                <div className="px-4">
                  <h3 className={`text-xl font-semibold mb-3 ${
                    theme === 'light' ? 'text-gray-800' : 'text-white'
                  }`}>{t("home.draw_methods.traditional.title")}</h3>
                  <p className={`${
                    theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                  }`}>
                    {t("home.draw_methods.traditional.description")}
                  </p>
                </div>
                <div className="bg-gradient-to-br from-purple-100 to-pink-200 dark:from-purple-900 dark:to-pink-900 p-1 rounded-2xl shadow-lg">
                  <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '1/1' }}>
                    <CdnLazyImage 
                      src={`/images-optimized/landing/Traditional-online-card-draw-${i18n.language}.webp`} 
                      alt={t("home.draw_methods.traditional.title")}
                      className="w-full h-full object-cover" 
                    />
                  </div>
                </div>
              </div>
              
              {/* 幸运数字选牌 */}
              <div className="space-y-4">
                <div className="px-4">
                  <h3 className={`text-xl font-semibold mb-3 ${
                    theme === 'light' ? 'text-gray-800' : 'text-white'
                  }`}>{t("home.draw_methods.lucky_number.title")}</h3>
                  <p className={`${
                    theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                  }`}>
                    {t("home.draw_methods.lucky_number.description")}
                  </p>
                </div>
                <div className="bg-gradient-to-br from-purple-100 to-pink-200 dark:from-purple-900 dark:to-pink-900 p-1 rounded-2xl shadow-lg">
                  <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '1/1' }}>
                    <CdnLazyImage 
                      src={`/images-optimized/landing/Lucky-Number-Picker-${i18n.language}.webp`} 
                      alt={t("home.draw_methods.lucky_number.title")}
                      className="w-full h-full object-cover" 
                    />
                  </div>
                </div>
              </div>
              
              {/* 自选塔罗牌解读 */}
              <div className="space-y-4">
                <div className="px-4">
                  <h3 className={`text-xl font-semibold mb-3 ${
                    theme === 'light' ? 'text-gray-800' : 'text-white'
                  }`}>{t("home.draw_methods.custom.title")}</h3>
                  <p className={`${
                    theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                  }`}>
                    {t("home.draw_methods.custom.description")}
                  </p>
                </div>
                <div className="bg-gradient-to-br from-purple-100 to-pink-200 dark:from-purple-900 dark:to-pink-900 p-1 rounded-2xl shadow-lg">
                  <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '1/1' }}>
                    <CdnLazyImage 
                      src={`/images-optimized/landing/Self-selected-Tarot-reading-${i18n.language}.webp`} 
                      alt={t("home.draw_methods.custom.title")}
                      className="w-full h-full object-cover" 
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* <div className="mt-28 sm:mt-36">
            <Suspense
              fallback={
                <div className="w-full h-[400px] bg-gray-900 rounded-lg animate-pulse" />
              }
            >
              <VideoSection
                videoSrc="images-optimized/videos/AI-Spread-Recommendations.webm"
                title={t("home.video_section_1.title")}
                description={t("home.video_section_1.description")}
                delayLoop={true}
              />
            </Suspense>
          </div>
          
          <Suspense
            fallback={
              <div className="w-full h-[400px] bg-gray-900 rounded-lg animate-pulse" />
            }
          >
            <VideoSection
              videoSrc="images-optimized/videos/Multiple-Tarot-Draw-Options.webm"
              title={t("home.video_section_2.title")}
              description={t("home.video_section_2.description")}
              isRightAligned={true}
              delayLoop={true}
            />
          </Suspense>

          <Suspense
            fallback={
              <div className="w-full h-[400px] bg-gray-900 rounded-lg animate-pulse" />
            }
          >
            <VideoSection
              videoSrc="images-optimized/videos/Free-Tarot-Card-Reading-Online.webm"
              title={t("home.video_section_3.title")}
              description={t("home.video_section_3.description")}
              delayLoop={true}
            />
          </Suspense>
          
          <Suspense
            fallback={
              <div className="w-full h-[400px] bg-gray-900 rounded-lg animate-pulse" />
            }
          >
            <VideoSection
              videoSrc="images-optimized/videos/Accurate-Online-Tarot-Reading-History.webm"
              title={t("home.video_section_4.title")}
              description={t("home.video_section_4.description")}
              isRightAligned={true}
              delayLoop={true}
            />
          </Suspense> */}

          <div className="max-w-4xl md:max-w-5xl lg:max-w-6xl mx-auto mt-36 px-4 sm:px-6 text-center">
            <div className="py-12">
              <h2 className={`text-2xl sm:text-3xl font-bold ${
                theme === 'light' ? 'text-purple-800' : 'text-white'
              }`}>
                {t("home.professional_section.title")}
              </h2>
              <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
              <p className={`${
                theme === 'light' ? 'text-gray-700' : 'text-gray-300'
              } text-lg max-w-3xl mx-auto`}>
                {t("home.professional_section.subtitle")}
              </p>

              <ReaderCarousel />

              <div className={`${
                theme === 'light' ? 'text-gray-700' : 'text-gray-300'
              } space-y-6 md:space-y-8 text-lg md:text-xl max-w-3xl md:max-w-4xl lg:max-w-5xl mx-auto leading-relaxed md:leading-relaxed`}>
                <p>{t("home.professional_section.paragraph1")}</p>
                <p>{t("home.professional_section.paragraph2")}</p>
              </div>
            </div>
          </div>

          {/* 新增博客板块 */}
          {/* <div className="max-w-6xl mx-auto mt-28 sm:mt-36 px-4 sm:px-6">
            <div className="text-center mb-10">
              <h2 className={`text-2xl sm:text-3xl font-bold ${
                theme === 'light' ? 'text-purple-800' : 'text-white'
              }`}>
                {t("home.blog_section.title")}
              </h2>
              <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
              <p className={`${
                theme === 'light' ? 'text-gray-700' : 'text-gray-300'
              } text-lg max-w-3xl mx-auto`}>
                {t("home.blog_section.subtitle")}
              </p>
            </div>

            {/* 博客文章列表 */}
            {/* <div className="space-y-8">
              {blogPostsByLang[i18n.language] && blogPostsByLang[i18n.language].slice(0, 3).map((post) => (
                <div 
                  key={`${i18n.language}-${post.id}-${post.slug}`}
                  onClick={() => navigate(`/blog/${post.slug}`)}
                  className={`rounded-xl overflow-hidden cursor-pointer transition-all duration-300 ${
                    theme === 'light' 
                      ? 'bg-white shadow-md hover:shadow-lg border border-gray-100' 
                      : 'bg-gray-800 shadow-lg hover:shadow-xl border border-gray-700'
                  }`}
                >
                  {/* 桌面端 - 左图右文 */}
                  {/* <div className="hidden md:flex flex-row h-[220px]">
                    {/* 文章封面图 - 左侧 */}
                    {/* <div className="w-1/3 overflow-hidden">
                      <CdnLazyImage 
                        src={post.coverImage} 
                        alt={post.title} 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    
                    {/* 文章内容 - 右侧 */}
                    {/* <div className="p-6 w-2/3 flex flex-col justify-between">
                      <div>
                        <div className={`${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} text-sm mb-2`}>{post.date}</div>
                        <h3 className={`text-xl font-bold ${theme === 'light' ? 'text-gray-800' : 'text-white'} mb-2`}>
                          {post.title}
                        </h3>
                        <p className={`${theme === 'light' ? 'text-gray-600' : 'text-gray-300'} text-sm mb-4 line-clamp-3`}>
                          {(() => {
                            const seoConfig = getSEOConfig(`/blog/${post.slug}`, i18n);
                            return seoConfig.description;
                          })()}
                        </p>
                      </div>
                      <div className="mt-4">
                        <div className="inline-flex items-center text-purple-500 text-sm font-medium hover:text-purple-400 group">
                          {t("home.blog_section.read_more")}
                          <svg className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* 移动端 - 图片在上，文案在下 */}
                  {/* <div className="md:hidden h-[280px]">
                    {/* 文章封面图 */}
                    {/* <div className="w-full h-[160px] overflow-hidden">
                      <CdnLazyImage 
                        src={post.coverImage} 
                        alt={post.title} 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    
                    {/* 文章内容 - 在图片下方 */}
                    {/* <div className={`p-4 h-[120px] flex flex-col justify-between ${
                      theme === 'light' ? 'bg-white' : 'bg-gray-800'
                    }`}>
                      <div>
                        <h3 className={`text-lg font-bold mb-1 ${
                          theme === 'light' ? 'text-gray-800' : 'text-white'
                        }`}>
                          {post.title}
                        </h3>
                        <p className={`text-sm line-clamp-2 ${
                          theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                        }`}>
                          {(() => {
                            const seoConfig = getSEOConfig(`/blog/${post.slug}`, i18n);
                            return seoConfig.description;
                          })()}
                        </p>
                      </div>
                      <div className="mt-2">
                        <div className="inline-flex items-center text-purple-500 text-sm font-medium">
                          {t("home.blog_section.read_more")}
                          <svg className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div> */}

            {/* 查看更多按钮 */}
            {/* <div className="text-center mt-10">
              <button
                onClick={() => navigate('/blog')}
                className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full hover:shadow-lg transition duration-300"
              >
                {t("home.blog_section.view_more")}
              </button>
            </div>
          </div> */}

          {/* 星座运势推广板块 */}
          <HoroscopePromoBlock 
            className="mt-28 sm:mt-36"
          />

          {/* 新增常见问题FAQ板块 */}
          <div className="max-w-4xl mx-auto mt-28 sm:mt-36 px-4 sm:px-6">
            <div className="text-center mb-10">
              <h2 className={`text-2xl sm:text-3xl font-bold ${
                theme === 'light' ? 'text-purple-800' : 'text-white'
              }`}>
                {t("home.faq.title")}
              </h2>
              <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
              <p className={`${
                theme === 'light' ? 'text-gray-700' : 'text-gray-300'
              } text-lg max-w-3xl mx-auto`}>
                {t("home.faq.subtitle")}
              </p>
            </div>

            <div className="space-y-6">
              <div className={`p-6 rounded-lg ${
                theme === 'light' ? 'bg-white shadow-md' : 'bg-gray-800 shadow-lg'
              }`}>
                <h3 className={`text-lg font-semibold mb-2 ${
                  theme === 'light' ? 'text-gray-800' : 'text-white'
                }`}>{t("home.faq.questions.accuracy.title")}</h3>
                <p className={`${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                }`}>
                  {t("home.faq.questions.accuracy.content")}
                </p>
              </div>

              <div className={`p-6 rounded-lg ${
                theme === 'light' ? 'bg-white shadow-md' : 'bg-gray-800 shadow-lg'
              }`}>
                <h3 className={`text-lg font-semibold mb-2 ${
                  theme === 'light' ? 'text-gray-800' : 'text-white'
                }`}>{t("home.faq.questions.effective_questions.title")}</h3>
                <p className={`${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                }`}>
                  {t("home.faq.questions.effective_questions.content")}
                </p>
              </div>

              <div className={`p-6 rounded-lg ${
                theme === 'light' ? 'bg-white shadow-md' : 'bg-gray-800 shadow-lg'
              }`}>
                <h3 className={`text-lg font-semibold mb-2 ${
                  theme === 'light' ? 'text-gray-800' : 'text-white'
                }`}>{t("home.faq.questions.intuitive_tarot.title")}</h3>
                <p className={`${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                }`}>
                  {t("home.faq.questions.intuitive_tarot.content")}
                </p>
              </div>

              <div className={`p-6 rounded-lg ${
                theme === 'light' ? 'bg-white shadow-md' : 'bg-gray-800 shadow-lg'
              }`}>
                <h3 className={`text-lg font-semibold mb-2 ${
                  theme === 'light' ? 'text-gray-800' : 'text-white'
                }`}>{t("home.faq.questions.taboos.title")}</h3>
                <p className={`${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                }`}>
                  {t("home.faq.questions.taboos.content")}
                </p>
              </div>

              <div className={`p-6 rounded-lg ${
                theme === 'light' ? 'bg-white shadow-md' : 'bg-gray-800 shadow-lg'
              }`}>
                <h3 className={`text-lg font-semibold mb-2 ${
                  theme === 'light' ? 'text-gray-800' : 'text-white'
                }`}>{t("home.faq.questions.online_vs_physical.title")}</h3>
                <p className={`${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                }`}>
                  {t("home.faq.questions.online_vs_physical.content")}
                </p>
              </div>

              <div className={`p-6 rounded-lg ${
                theme === 'light' ? 'bg-white shadow-md' : 'bg-gray-800 shadow-lg'
              }`}>
                <h3 className={`text-lg font-semibold mb-2 ${
                  theme === 'light' ? 'text-gray-800' : 'text-white'
                }`}>{t("home.faq.questions.learning.title")}</h3>
                <p className={`${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                }`}>
                  {t("home.faq.questions.learning.content")}
                </p>
              </div>
            </div>
          </div>

          

          {/* 新增用户评价/见证板块 */}
          <div className="max-w-6xl mx-auto mt-28 sm:mt-36 px-4 sm:px-6">
            <div className="text-center mb-10">
              <h2 className={`text-2xl sm:text-3xl font-bold ${
                theme === 'light' ? 'text-purple-800' : 'text-white'
              }`}>
                {t("home.testimonials.title")}
              </h2>
              <div className="w-12 h-0.5 bg-purple-500 mx-auto mt-3 mb-4"></div>
              <p className={`${
                theme === 'light' ? 'text-gray-700' : 'text-gray-300'
              } text-lg max-w-3xl mx-auto`}>
                {t("home.testimonials.subtitle")}
              </p>
            </div>

            <div className="flex flex-col md:flex-row gap-8">
              {/* 左侧评分部分 */}
              <div className="md:w-1/3">
                <h3 className={`text-2xl font-bold mb-4 ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
                  {t("home.testimonials.ratings.title")}
                </h3>
                
                {/* 总体评分 */}
                <div className="flex items-center mb-4">
                  <span className="text-4xl font-bold mr-3">4.7</span>
                  <div className="flex">
                    {[...Array(4)].map((_, i) => (
                      <svg key={`star-rating-${i}`} xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-400" viewBox="0 0 20 20" fill="currentColor" stroke="#FBBF24" strokeWidth="0.5">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" viewBox="0 0 20 20" fill="currentColor" stroke="#FBBF24" strokeWidth="0.5">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  </div>
                </div>
                <p className={`${theme === 'light' ? 'text-gray-600' : 'text-gray-300'} mb-6`}>{t("home.testimonials.ratings.based_on", {count: 134})}</p>

                {/* 评分分布 */}
                <div className="space-y-2 mb-8">
                  {/* 5星 */}
                  <div className="flex items-center">
                    <span className="w-4 text-sm mr-2">5</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor" stroke="#FBBF24" strokeWidth="0.5">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <div className="flex-1 h-2 bg-gray-200 rounded-full">
                      <div className="h-2 bg-yellow-400 rounded-full" style={{ width: '75%' }}></div>
                    </div>
                    <span className="w-12 text-sm text-right ml-2">75%</span>
                  </div>
                  {/* 4星 */}
                  <div className="flex items-center">
                    <span className="w-4 text-sm mr-2">4</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor" stroke="#FBBF24" strokeWidth="0.5">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <div className="flex-1 h-2 bg-gray-200 rounded-full">
                      <div className="h-2 bg-yellow-400 rounded-full" style={{ width: '10%' }}></div>
                    </div>
                    <span className="w-12 text-sm text-right ml-2">10%</span>
                  </div>
                  {/* 3星 */}
                  <div className="flex items-center">
                    <span className="w-4 text-sm mr-2">3</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor" stroke="#FBBF24" strokeWidth="0.5">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <div className="flex-1 h-2 bg-gray-200 rounded-full">
                      <div className="h-2 bg-yellow-400 rounded-full" style={{ width: '25%' }}></div>
                    </div>
                    <span className="w-12 text-sm text-right ml-2">25%</span>
                  </div>
                  {/* 2星 */}
                  <div className="flex items-center">
                    <span className="w-4 text-sm mr-2">2</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white mr-2" viewBox="0 0 20 20" fill="currentColor" stroke="#FBBF24" strokeWidth="0.5">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <div className="flex-1 h-2 bg-gray-200 rounded-full">
                      <div className="h-2 bg-yellow-400 rounded-full" style={{ width: '0%' }}></div>
                    </div>
                    <span className="w-12 text-sm text-right ml-2">0%</span>
                  </div>
                  {/* 1星 */}
                  <div className="flex items-center">
                    <span className="w-4 text-sm mr-2">1</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white mr-2" viewBox="0 0 20 20" fill="currentColor" stroke="#FBBF24" strokeWidth="0.5">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <div className="flex-1 h-2 bg-gray-200 rounded-full">
                      <div className="h-2 bg-yellow-400 rounded-full" style={{ width: '0%' }}></div>
                    </div>
                    <span className="w-12 text-sm text-right ml-2">0%</span>
                  </div>
                </div>

                {/* We value your opinion */}
                <div>
                  <h3 className={`text-xl font-bold mb-3 ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
                    {t("home.testimonials.feedback.title")}
                  </h3>
                  <p className={`${theme === 'light' ? 'text-gray-600' : 'text-gray-300'} mb-4`}>
                    {t("home.testimonials.feedback.description")}
                  </p>
                  <button 
                    onClick={() => navigate('/feedback')}
                    className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full hover:shadow-lg transition duration-300">
                    {t("home.testimonials.feedback.button")}
                  </button>
                </div>
              </div>

              {/* 右侧评价内容 */}
              <div className="md:w-2/3 space-y-8 md:pl-12">
                {/* 第一个评价 */}
                <div>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full flex items-center justify-center bg-purple-600 text-white font-semibold text-xl">
                      {t("home.testimonials.reviews.user1.initial")}
                    </div>
                    <div className="ml-3">
                      <p className={`font-medium ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
                        {t("home.testimonials.reviews.user1.name")}
                      </p>
                      <div className="flex mt-1">
                        {[...Array(5)].map((_, i) => (
                          <svg key={`user1-star-${i}`} xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor" stroke="#FBBF24" strokeWidth="0.5">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                      </div>
                    </div>
                  </div>
                  <p className={`${theme === 'light' ? 'text-gray-600' : 'text-gray-300'} mb-6`}>
                    {t("home.testimonials.reviews.user1.content")}
                  </p>
                </div>

                {/* 第二个评价 */}
                <div>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full flex items-center justify-center bg-pink-600 text-white font-semibold text-xl">
                      {t("home.testimonials.reviews.user2.initial")}
                    </div>
                    <div className="ml-3">
                      <p className={`font-medium ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
                        {t("home.testimonials.reviews.user2.name")}
                      </p>
                      <div className="flex mt-1">
                        {[...Array(5)].map((_, i) => (
                          <svg key={`user2-star-${i}`} xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor" stroke="#FBBF24" strokeWidth="0.5">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                      </div>
                    </div>
                  </div>
                  <p className={`${theme === 'light' ? 'text-gray-600' : 'text-gray-300'} mb-6`}>
                    {t("home.testimonials.reviews.user2.content")}
                  </p>
                </div>

                {/* 第三个评价 */}
                <div>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full flex items-center justify-center bg-indigo-600 text-white font-semibold text-xl">
                      {t("home.testimonials.reviews.user3.initial")}
                    </div>
                    <div className="ml-3">
                      <p className={`font-medium ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
                        {t("home.testimonials.reviews.user3.name")}
                      </p>
                      <div className="flex mt-1">
                        {[...Array(4)].map((_, i) => (
                          <svg key={`user3-star-${i}`} xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor" stroke="#FBBF24" strokeWidth="0.5">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor" stroke="#FBBF24" strokeWidth="0.5">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                  <p className={`${theme === 'light' ? 'text-gray-600' : 'text-gray-300'} mb-6`}>
                    {t("home.testimonials.reviews.user3.content")}
                  </p>
                </div>
              </div>
            </div>
          </div>


          <Suspense
            fallback={
              <div className="w-full h-[200px] bg-gray-900 rounded-lg animate-pulse" />
            }
          >
            <div className="spotlight-section py-24 md:py-32 mt-16">
              <div className="max-w-3xl mx-auto px-2 sm:px-4">
                <SpotlightCard
                  className="custom-spotlight-card"
                  spotlightColor="rgba(0, 229, 255, 0.2)"
                >
                  <div className="p-4 sm:p-8 text-center">
                    <h3
                      className="text-2xl md:text-3xl font-semibold mb-4"
                      style={{
                        background: theme === 'light' 
                          ? "none" 
                          : "linear-gradient(135deg, #E2E8FF, #FFF1F2, #FAF5FF)",
                        WebkitBackgroundClip: theme === 'light' ? "inherit" : "text",
                        WebkitTextFillColor: theme === 'light' ? "#000" : "transparent",
                        color: theme === 'light' ? "#000" : "inherit"
                      }}
                    >
                      {t("home.explore_section.title")}
                    </h3>
                    <p className={`${
                      theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                    } text-lg md:text-xl mb-6 px-1`}>
                      {t("home.explore_section.description")}
                    </p>
                    <div className="flex justify-center">
                      <motion.button
                        onClick={handleStartReading}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="px-6 py-3 rounded-full"
                        style={{
                          background:
                            theme === 'light'
                              ? "linear-gradient(135deg, rgba(124, 58, 237, 0.7), rgba(168, 85, 247, 0.7))"
                              : "linear-gradient(135deg, rgba(124, 58, 237, 0.8), rgba(168, 85, 247, 0.8))",
                          boxShadow: theme === 'light' 
                            ? "0 0 20px rgba(168, 85, 247, 0.4)"
                            : "0 0 20px rgba(168, 85, 247, 0.5)",
                          color: 'white',
                        }}
                      >
                        {t("home.explore_section.button")}
                      </motion.button>
                    </div>
                  </div>
                </SpotlightCard>
              </div>
            </div>
          </Suspense>


        </div>
      </main>

      {/* Partner Links Section */}
      <div className="flex flex-wrap items-center justify-center gap-4 py-6">
        {/* Only show these badges in English */}
        {i18n.language === 'en' && (
          <>
            <a href="https://theresanaiforthat.com/ai/tarotqa/?ref=featured&v=6194044" target="_blank" rel="nofollow"><img width="300" src="https://media.theresanaiforthat.com/featured-on-taaft.png?width=600" /></a>

            {/* topAI.tools Spotlight */}
            <a href="https://topai.tools/t/tarotqa?ref=embed"><img src="https://topai.tools/assets/img/topai.tools.gif" style={{maxWidth:"300px",maxHeight:"80px"}} alt="tarotqa Featured on topAI.tools" /></a>
          </>
        )}


      </div>

      <Footer showBeian={true} />
    </div>
  );
};

export default NewPage;
